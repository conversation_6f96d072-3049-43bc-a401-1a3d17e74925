// ignore_for_file: deprecated_member_use, unused_local_variable, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:nb_utils/nb_utils.dart'; // استخدام للتحقق من الاتصال بالإنترنت وعناصر واجهة المستخدم

import '../../Provider/product_provider.dart';
import '../../constant.dart';
import '../../currency.dart'; // استخدام متغير currency للعملة
import '../../empty_screen_widget.dart';
import '../../model/product_model.dart';

import '../Warehouse/warehouse_model.dart';
import 'stock_movement_details.dart';

class StockList extends StatefulWidget {
  const StockList({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _StockListState createState() => _StockListState();
}

class _StockListState extends State<StockList>
    with SingleTickerProviderStateMixin {
  num totalStock = 0;
  double totalSalePrice = 0;
  double totalParPrice = 0;
  String? productName;
  int count = 0;

  // متغيرات للتصفية - هذه المتغيرات ضرورية للتصفية المستقبلية
  bool _showLowStock = false; // للتبديل بين عرض المنتجات منخفضة المخزون فقط
  final String _selectedCategory =
      'الكل'; // لتصفية المنتجات حسب الفئة - نستخدم final لأنها لا تتغير
  String _selectedWarehouse = 'الكل'; // لتصفية المنتجات حسب المخزن
  List<String> _categories = ['الكل']; // قائمة الفئات المتاحة
  List<String> _warehouses = ['الكل']; // قائمة المخازن المتاحة

  // متغيرات للبحث
  final TextEditingController _searchController = TextEditingController();

  // متغير للتحكم في علامات التبويب
  late TabController _tabController;

  WidgetRef? _ref;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // إضافة مستمع للتغييرات في حقل البحث
    _searchController.addListener(() {
      setState(() {
        productName = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  // دالة لتحديث البيانات
  Future<void> _refreshData() async {
    if (_ref != null) {
      setState(() {
        totalStock = 0;
        totalSalePrice = 0;
        totalParPrice = 0;
        count = 0;
      });

      // استخدام nb_utils للتحقق من الاتصال بالإنترنت
      bool isConnected = await isNetworkAvailable();
      if (!isConnected) {
        toast('لا يوجد اتصال بالإنترنت', gravity: ToastGravity.BOTTOM);
      }

      return _ref!.refresh(productProvider);
    }
    return Future.value();
  }

  // دالة لاستخراج الفئات والمخازن من المنتجات
  void _extractCategoriesAndWarehouses(List<ProductModel> products) {
    Set<String> categoriesSet = {'الكل'};
    Set<String> warehousesSet = {'الكل'};

    for (var product in products) {
      categoriesSet.add(product.productCategory);
      warehousesSet.add(product.warehouseName);
    }

    setState(() {
      _categories = categoriesSet.toList();
      _warehouses = warehousesSet.toList();
    });
  }

  // دالة لتصفية المنتجات
  List<ProductModel> _filterProducts(List<ProductModel> products) {
    // استخدام _categories للتحقق من وجود فئات
    if (_categories.length <= 1) {
      // إذا لم تكن هناك فئات، نستخدم الفئات من المنتجات
      Set<String> categoriesSet = {'الكل'};
      for (var product in products) {
        categoriesSet.add(product.productCategory);
      }
      _categories = categoriesSet.toList();
    }

    return products.where((product) {
      // تصفية حسب البحث
      bool matchesSearch = productName == null ||
          productName!.isEmpty ||
          product.productName
              .toLowerCase()
              .contains(productName!.toLowerCase());

      // تصفية حسب المخزون المنخفض
      bool matchesLowStock = !_showLowStock ||
          (num.tryParse(product.productStock) ?? 0) <= product.lowerStockAlert;

      // تصفية حسب الفئة
      bool matchesCategory = _selectedCategory == 'الكل' ||
          product.productCategory == _selectedCategory;

      // تصفية حسب المخزن
      bool matchesWarehouse = _selectedWarehouse == 'الكل' ||
          product.warehouseName == _selectedWarehouse;

      return matchesSearch &&
          matchesLowStock &&
          matchesCategory &&
          matchesWarehouse;
    }).toList();
  }

  // دالة لعرض المبلغ بالجنيه المصري
  String formatCurrency(double amount) {
    // استخدام متغير currency من currency.dart للتنسيق
    if (currency == 'جنية') {
      return '$currency ${myFormat.format(amount)}';
    } else {
      return '$currency ${myFormat.format(amount)}';
    }
  }

  NumberFormat decimalFormat = NumberFormat.decimalPattern('en_US');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          lang.S.of(context).currentStock,
          style: GoogleFonts.poppins(
            color: Colors.white,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0.0,
        actions: [
          // زر تصدير البيانات
          IconButton(
            icon: const Icon(Icons.file_download, color: Colors.white),
            onPressed: () {
              // سيتم تنفيذ تصدير البيانات لاحقًا
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('سيتم تنفيذ تصدير البيانات قريبًا'),
                  backgroundColor: Colors.green,
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer(
        builder: (context, ref, __) {
          // حفظ مرجع ref للاستخدام لاحقًا
          _ref = ref;
          final providerData = ref.watch(productProvider);
          final wareHouseList = ref.watch(warehouseProvider);

          // استخراج الفئات والمخازن عند تحميل البيانات
          providerData.whenData((products) {
            if (products.isNotEmpty) {
              _extractCategoriesAndWarehouses(products);
            }
          });

          return Container(
            alignment: Alignment.topCenter,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(30),
                topLeft: Radius.circular(30),
              ),
            ),
            child: RefreshIndicator(
              onRefresh: _refreshData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [
                      // علامات التبويب
                      TabBar(
                        controller: _tabController,
                        labelColor: kMainColor,
                        unselectedLabelColor: Colors.grey,
                        indicatorColor: kMainColor,
                        tabs: const [
                          Tab(text: 'جميع المنتجات'),
                          Tab(text: 'المنتجات منخفضة المخزون'),
                        ],
                        onTap: (index) {
                          setState(() {
                            _showLowStock = index == 1;
                          });
                        },
                      ),
                      const SizedBox(height: 10),

                      // محتوى الشاشة
                      providerData.when(
                        data: (product) {
                          if (count == 0) {
                            count++;
                            // حساب إجماليات المخزون والقيم
                            for (var element in product) {
                              totalStock = totalStock +
                                  (num.tryParse(element.productStock) ?? 0);

                              totalSalePrice = totalSalePrice +
                                  ((num.tryParse(element.productStock) ?? 0) *
                                      (num.tryParse(element.productSalePrice) ??
                                          0));

                              totalParPrice = totalParPrice +
                                  ((num.tryParse(element.productStock) ?? 0) *
                                      (num.tryParse(
                                              element.productPurchasePrice) ??
                                          0));
                            }
                          }

                          // تصفية المنتجات
                          final filteredProducts = _filterProducts(product);

                          return filteredProducts.isNotEmpty
                              ? Column(
                                  children: [
                                    // بطاقة إجماليات المخزون
                                    Padding(
                                      padding: const EdgeInsets.all(20.0),
                                      child: Container(
                                        height: 100,
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          color: kMainColor.withOpacity(0.1),
                                          border: Border.all(
                                              width: 1, color: kMainColor),
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(15)),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            // إجمالي المخزون
                                            Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  myFormat.format(totalStock),
                                                  style: const TextStyle(
                                                    color: Colors.green,
                                                    fontSize: 20,
                                                  ),
                                                ),
                                                const SizedBox(height: 10),
                                                Text(
                                                  lang.S.of(context).totalStock,
                                                  style: const TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Container(
                                              width: 1,
                                              height: 60,
                                              color: kMainColor,
                                            ),
                                            // إجمالي القيمة
                                            Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  formatCurrency(totalParPrice),
                                                  style: const TextStyle(
                                                    color: Colors.orange,
                                                    fontSize: 20,
                                                  ),
                                                ),
                                                const SizedBox(height: 10),
                                                Text(
                                                  lang.S.of(context).totalPrice,
                                                  style: const TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),

                                    // حقل البحث
                                    TextField(
                                      controller: _searchController,
                                      decoration: InputDecoration(
                                        contentPadding: const EdgeInsets.only(
                                            left: 8.0, right: 8.0),
                                        floatingLabelBehavior:
                                            FloatingLabelBehavior.never,
                                        labelText:
                                            lang.S.of(context).productName,
                                        hintText:
                                            lang.S.of(context).enterProductName,
                                        prefixIcon: const Icon(Icons.search),
                                        border: const OutlineInputBorder(),
                                        suffixIcon: Container(
                                          padding: const EdgeInsets.all(8),
                                          child: DropdownButtonHideUnderline(
                                            child: DropdownButton<String>(
                                              value: _selectedWarehouse,
                                              isDense: true,
                                              items: _warehouses
                                                  .map((String warehouse) {
                                                return DropdownMenuItem<String>(
                                                  value: warehouse,
                                                  child: Text(warehouse,
                                                      style: const TextStyle(
                                                          fontSize: 12)),
                                                );
                                              }).toList(),
                                              onChanged: (String? newValue) {
                                                setState(() {
                                                  _selectedWarehouse =
                                                      newValue!;
                                                });
                                              },
                                              hint: const Text('المخزن',
                                                  style:
                                                      TextStyle(fontSize: 12)),
                                              icon: const Icon(
                                                  Icons.arrow_drop_down,
                                                  size: 20),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),

                                    // قائمة المنتجات
                                    const SizedBox(height: 10),
                                    Container(
                                      padding: const EdgeInsets.all(10),
                                      color: kMainColor.withOpacity(0.2),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 2,
                                            child: Text(
                                              lang.S.of(context).product,
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Text(
                                              lang.S.of(context).qty,
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Text(
                                              lang.S.of(context).purchase,
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    ListView.builder(
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      shrinkWrap: true,
                                      itemCount: filteredProducts.length,
                                      itemBuilder: (context, index) {
                                        final isLowStock = (num.tryParse(
                                                    filteredProducts[index]
                                                        .productStock) ??
                                                0) <=
                                            filteredProducts[index]
                                                .lowerStockAlert;

                                        return Padding(
                                          padding: const EdgeInsets.all(10.0),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                flex: 2,
                                                child: InkWell(
                                                  onTap: () {
                                                    // عرض تفاصيل حركة المخزون
                                                    Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) =>
                                                            StockMovementDetails(
                                                          product:
                                                              filteredProducts[
                                                                  index],
                                                          warehouseId:
                                                              _selectedWarehouse,
                                                          warehouseName:
                                                              _selectedWarehouse,
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        filteredProducts[index]
                                                            .productName,
                                                        style:
                                                            GoogleFonts.poppins(
                                                          color: isLowStock
                                                              ? Colors.red
                                                              : Colors.black,
                                                          fontSize: 16.0,
                                                        ),
                                                      ),
                                                      Text(
                                                        filteredProducts[index]
                                                            .brandName,
                                                        style:
                                                            GoogleFonts.poppins(
                                                          color: isLowStock
                                                              ? Colors.red
                                                              : kGreyTextColor,
                                                          fontSize: 12.0,
                                                        ),
                                                      ),
                                                      Text(
                                                        'المخزن: ${filteredProducts[index].warehouseName}',
                                                        style:
                                                            GoogleFonts.poppins(
                                                          color: Colors.blue,
                                                          fontSize: 12.0,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: Text(
                                                  myFormat.format(num.tryParse(
                                                          filteredProducts[
                                                                  index]
                                                              .productStock) ??
                                                      0),
                                                  style: GoogleFonts.poppins(
                                                    color: isLowStock
                                                        ? Colors.red
                                                        : Colors.black,
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: Text(
                                                  formatCurrency(double.tryParse(
                                                          filteredProducts[
                                                                  index]
                                                              .productPurchasePrice) ??
                                                      0),
                                                  style: GoogleFonts.poppins(
                                                    color: isLowStock
                                                        ? Colors.red
                                                        : Colors.black,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                )
                              : const Center(
                                  child: Padding(
                                    padding: EdgeInsets.only(top: 60),
                                    child: EmptyScreenWidget(),
                                  ),
                                );
                        },
                        error: (e, stack) {
                          return Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error,
                                  color: Colors.red, size: 50),
                              const SizedBox(height: 10),
                              Text('حدث خطأ أثناء تحميل البيانات'),
                              const SizedBox(height: 10),
                              ElevatedButton(
                                onPressed: _refreshData,
                                child: Text('إعادة المحاولة'),
                              ),
                            ],
                          );
                        },
                        loading: () {
                          return const Center(
                            child: Padding(
                              padding: EdgeInsets.only(top: 60),
                              child: CircularProgressIndicator(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: Consumer(
        builder: (context, ref, _) {
          final providerData = ref.watch(productProvider);
          return providerData.when(
            data: (product) => product.isNotEmpty
                ? Container(
                    color: Colors.white,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: kMainColor.withOpacity(0.2),
                            border: const Border(
                                bottom:
                                    BorderSide(width: 1, color: Colors.grey)),
                          ),
                          padding: const EdgeInsets.all(10),
                          child: Padding(
                            padding: const EdgeInsets.only(left: 15, right: 15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // معلومات المخزن والمبيعات
                                const SizedBox(height: 10),
                                // معلومات المخزن
                                SizedBox(
                                  width: double.infinity,
                                  child: Text(
                                    'إجمالي المبيعات: ${formatCurrency(totalSalePrice)}',
                                    textAlign: TextAlign.center,
                                    style: GoogleFonts.poppins(
                                      color: Colors.grey[600],
                                      fontSize: 13.0,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox(),
            error: (e, stack) => const SizedBox(),
            loading: () => const SizedBox(),
          );
        },
      ),
    );
  }
}
